{% extends "oscar/layout_storefront.html" %}


{% load history_tags %}
{% load custom_currency_filters %}
{% load reviews_tags %}
{% load static %}
{% load custom_product_tags %}
{% load display_tags %}
{% load i18n %}
{% load cache %}
{% load custom_purchase_info_tags %}
{% load contact_info_tags %}
{% load tz %}

{% block title %}
    {{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}
{% endblock %}

{% block description %}
    {{ product.get_meta_description|default:""|striptags }}
{% endblock %}

{% block keywords %}
    {{ product.get_meta_keywords|default:""|striptags }}
{% endblock %}

{% block open_graph %}
{% purchase_info_for_product request product as session %}
<meta property="og:title" content="{{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}" >
<meta property="og:description" content="{{ product.get_meta_description|default:""|striptags }}" >
<meta property="og:site_name" content="{{ site.domain }}" >
<meta property="og:image" content="{{ product.get_absolute_main_image_url }}" >
<meta property="product:price:amount" content="{{ session.price.incl_tax|unlocalize }}" >
<meta property="product:price:currency" content="EUR" >
<meta property="product:availability" content="{% if session.availability.is_available_to_buy %}in stock{% else %}out of stock{% endif %}" >
<meta property="og:product:condition" content="{{ product.get_condition_display }}" >
<meta property="og:url" content="{{ current_site }}" >
<meta property="og:type" content="product" >
{% endblock %}

{% block extrahead %}
    {% purchase_info_for_product request product as session %}
    <script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "Product",
        "name": "{{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}",
        "brand": {"@type": "Brand", "name": "{{ product.get_main_manufacturer }}"},
        "mpn": "{{ product.original_code }}",
        "category": "{{ product.get_main_category }}",
        {% if product.color %}"color": "{{ product.color }}",{% endif %}
        {% if product.weight %}"weight": {"value": "{{ product.weight }}", "unitText": "kg"},{% endif %}
        {% if product.height %}"height": "{{ product.height }} cm",{% endif %}
        {% if product.width %}"width": "{{ product.width }} cm",{% endif %}
        {% if product.length %}"depth": "{{ product.length  }} cm",{% endif %}
        "image": [
            {% for image in product.images.all %}
                "https://{{ request.get_host }}/media/{{ image.original.name|escapejs }}"{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        "description": "{{ product.get_meta_description|default:"" }}",
        "review": {
            "@type": "Review",
            "reviewBody": "{% if product.condition == 'used' %}In good, working condition. This is a used part that has been tested and verified to be functioning properly.{% elif product.condition == 'new' %}Brand new part in original condition.{% elif product.condition == 'broken' %}Part is damaged or not functioning, suitable for repairs or parts.{% endif %}",
            "author": {
                "@type": "Person",
                "name": "Quality Control Specialist"
            },
            "reviewRating": {
                "@type": "Rating",
                "ratingValue": "{% if product.condition == 'new' %}5{% elif product.condition == 'used' %}4{% else %}2{% endif %}",
                "bestRating": "5",
                "worstRating": "1"
            }
        },
        "offers": {
            "@type": "Offer",
            "url": "https://{{ request.get_host }}{{ product.get_absolute_url }}",
            "priceCurrency": "EUR",
            "price": "{{ session.price.incl_tax|unlocalize }}",
            "priceValidUntil": "{% now 'Y' as current_year %}{{ current_year|add:'1' }}-{% now 'm-d' %}",
            {% if product.condition == 'used' %}
            "itemCondition": "http://schema.org/UsedCondition",
            {% elif product.condition == 'new' %}
            "itemCondition": "http://schema.org/NewCondition",
            {% elif product.condition == 'broken' %}
            "itemCondition": "http://schema.org/DamagedCondition",
            {% endif %}
            "availability": "{% if session.availability.is_available_to_buy %}http://schema.org/InStock{% else %}http://schema.org/OutOfStock{% endif %}",
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                {% if request.LANGUAGE_CODE == 'de' and request.branch.branch == 'de' %}
                "returnPolicyCountry": "DE",
                "applicableCountry": "DE",
                {% elif request.LANGUAGE_CODE == 'pl' and request.branch.branch == 'pl' %}
                "returnPolicyCountry": "PL",
                "applicableCountry": "PL",
                {% elif request.LANGUAGE_CODE == 'lt' and request.branch.branch == 'lt' %}
                "returnPolicyCountry": "LT",
                "applicableCountry": "LT",
                {% elif request.LANGUAGE_CODE == 'fr' and request.branch.branch == 'fr' %}
                "returnPolicyCountry": "FR",
                "applicableCountry": "FR",
                {% elif request.LANGUAGE_CODE == 'es' and request.branch.branch == 'es' %}
                "returnPolicyCountry": "ES",
                "applicableCountry": "ES",
                {% endif %}
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "merchantReturnDays": 14,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/ReturnFeesCustomerResponsibility"
            }
        }
    }
    </script>
    {{ block.super }}
{% endblock extrahead %}

{% block header %}{% endblock %}

{% block breadcrumbs %}
<ul class="breadcrumb">
    <li>
        <a href="{% url 'promotions:home' %}">{% trans "Home" %}</a>
    </li>
    {% with category=product.categories.all.0 %}
        {% for c in category.get_ancestors %}
        <li>
            <a href="{{ c.get_absolute_url }}">{{ c.get_name }}</a>
        </li>
        {% endfor %}
        <li class="active">{{ product.get_title }}</li>
    {% endwith %}
</ul>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{
    selectedImage: 0,
    productDetailsOpen: true,
    shippingOpen: false,
    contactsOpen: false
}">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

        {% block product_gallery %}
        <!-- Product Gallery -->
        <div class="space-y-4">
            {% cache 500 product_galery LANGUAGE_CODE product.id request.branch %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    {% with all_images=product.images.all %}
                    {% if all_images|length > 1 %}
                        <!-- Main Image -->
                        <div class="aspect-square bg-gray-50 flex items-center justify-center p-4">
                            {% for image in all_images %}
                                {% oscar_thumbnail image.original "500x500" upscale=False crop="center" as thumb %}
                                <img x-show="selectedImage === {{ forloop.counter0 }}"
                                     src="{{ thumb.url }}"
                                     alt="{{ product.get_title }}"
                                     class="max-w-full max-h-full object-contain">
                            {% endfor %}
                        </div>

                        <!-- Thumbnail Gallery -->
                        <div class="flex gap-2 p-4 overflow-x-auto bg-gray-50">
                            {% for image in all_images %}
                                {% oscar_thumbnail image.original "80x80" crop="center" as thumb %}
                                <button @click="selectedImage = {{ forloop.counter0 }}"
                                        :class="selectedImage === {{ forloop.counter0 }} ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'"
                                        class="flex-shrink-0 w-16 h-16 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all">
                                    <img src="{{ thumb.url }}" alt="{{ product.get_title }} - {{ forloop.counter }}"
                                         class="w-full h-full object-cover">
                                </button>
                            {% endfor %}
                        </div>
                    {% else %}
                        <!-- Single Image -->
                        <div class="aspect-square bg-gray-50 flex items-center justify-center p-4">
                            {% with image=product.primary_image %}
                                {% oscar_thumbnail image.original "500x500" upscale=False crop="center" as thumb %}
                                <img src="{{ thumb.url }}" alt="{{ product.get_title }}"
                                     class="max-w-full max-h-full object-contain">
                            {% endwith %}
                        </div>
                    {% endif %}
                    {% endwith %}
                </div>
            {% endcache %}

            <!-- Product Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p class="text-sm text-blue-800">
                    {% trans 'Before buying this item please make sure if this part actually fits. Pay attention to the part number in the description and picture to match your parts number.' %}
                </p>
            </div>
        </div>
        {% endblock product_gallery %}

        {% block product_main %}
        <!-- Product Information -->
        <div class="space-y-6">
            <!-- Product Title -->
            <div>
                <h1 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                    {{ product.get_title }}
                </h1>
            </div>

            <!-- Price and Rating -->
            {% purchase_info_for_product request product as session %}
            {% discount_info_for_product request product as discount_info %}
            <div class="space-y-4">
                <div class="text-3xl font-bold text-gray-900">
                    {% if session.price.exists and session.price.is_tax_known %}
                        {{ session.price.incl_tax|user_currency:user_currency }}
                    {% endif %}
                    {% if discount_info.has_discount %}
                        <span class="text-xl text-gray-500 line-through ml-3">
                            {{ discount_info.old_price_incl_tax|user_currency:user_currency }}
                        </span>
                    {% endif %}
                </div>

                <!-- Rating Stars -->
                <div class="flex items-center gap-2">
                    <div class="flex text-blue-500">
                        {% for i in "1234" %}
                            <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20">
                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                            </svg>
                        {% endfor %}
                        <svg class="w-5 h-5 text-gray-300 fill-current" viewBox="0 0 20 20">
                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Product Description -->
            <div class="text-gray-600 leading-relaxed">
                <p>{{ product.get_meta_description|default:"The perfect midpoint between shopping tote and comfy backpack. With convertible straps, you can hand carry, should sling, or backpack this convenient and spacious bag. The zip top and durable canvas construction keeps your goods protected for all-day use."|striptags }}</p>
            </div>

            <!-- Color Selection (if applicable) -->
            {% if product.color %}
            <div class="space-y-3">
                <h3 class="text-sm font-medium text-gray-900">{% trans "Color" %}</h3>
                <div class="flex gap-2">
                    <button class="w-8 h-8 rounded-full border-2 border-gray-300 bg-gray-800 ring-2 ring-blue-500 ring-offset-2"></button>
                    <button class="w-8 h-8 rounded-full border-2 border-gray-300 bg-blue-600 hover:ring-2 hover:ring-blue-200"></button>
                </div>
            </div>
            {% endif %}

            {% if session.availability.is_available_to_buy %}
            <!-- Add to Cart Form -->
            <form action="{% url 'basket:add' pk=product.pk %}" method="post" class="space-y-6" x-data="{ quantity: 1 }">
                {% csrf_token %}
                <input id="id_product_id" name="product_id" value="{{ product.id }}" type="hidden">

                <!-- Quantity and Add to Cart -->
                <div class="flex gap-4">
                    <div class="flex items-center border border-gray-300 rounded-lg bg-white">
                        <button type="button" @click="quantity = Math.max(1, quantity - 1)"
                                class="px-3 py-2 text-gray-500 hover:text-gray-700 focus:outline-none">-</button>
                        <input type="number" name="quantity" id="id_quantity" x-model="quantity" min="1"
                               class="w-16 text-center border-0 focus:ring-0 focus:outline-none bg-transparent"
                               aria-label="{% trans 'Quantity' %}">
                        <button type="button" @click="quantity = quantity + 1"
                                class="px-3 py-2 text-gray-500 hover:text-gray-700 focus:outline-none">+</button>
                    </div>

                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        {% trans 'Add to bag' %}
                    </button>

                    <button type="button" class="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                    </button>
                </div>

                <!-- Stock Info -->
                <div class="text-sm text-gray-600">
                    {% trans 'Stock' %}: {{ session.stockrecord.num_in_stock }}
                </div>

                <!-- Offer Price Link -->
                {% if product.active %}
                <div class="pt-2">
                    <a href="{% url 'price-offer' pk=product.pk %}" class="text-blue-600 hover:text-blue-700 text-sm font-medium underline">
                        {% trans 'Offer your price' %}
                    </a>
                </div>
                {% endif %}
            </form>
            {% endif %}
        </div>
        {% endblock product_main %}
    </div>

    <!-- Collapsible Sections -->
    <div class="mt-12 space-y-6">

        <!-- Product Details Section (Expanded by default) -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
            <button @click="productDetailsOpen = !productDetailsOpen"
                    class="w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">{% trans 'Product Details' %}</h3>
                <svg :class="productDetailsOpen ? 'rotate-180' : ''"
                     class="w-5 h-5 text-gray-500 transition-transform"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
            </button>
            <div x-show="productDetailsOpen"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 class="px-6 py-4 bg-white border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'Name' %}</span>
                            <span class="font-medium text-gray-900">{{ product.get_main_category|safe }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans "Commercial ID" %}</span>
                            <span class="font-medium text-gray-900">{{ product.commercial_id }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans "Product code (MPN)" %}</span>
                            <span class="font-medium text-gray-900" title="{{ product.original_code }}">{{ product.original_code|truncatewords:4|default:"--" }}</span>
                        </div>
                        {% purchase_info_for_product request product as session %}
                        {% if session.price.exists and session.price.is_tax_known %}
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans "Price (incl. tax)" %}</span>
                            <span class="font-medium text-gray-900">{{ session.price.incl_tax|user_currency:user_currency }}</span>
                        </div>
                        {% endif %}
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans "Condition" %}</span>
                            <span class="font-medium text-gray-900">{{ product.get_condition_display }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'Drive position' %}</span>
                            <span class="font-medium text-gray-900">{{ product.get_drive_position_display|default:"--" }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'Attributes' %}</span>
                            <span class="font-medium text-gray-900" title="{{ product.get_extra_attributes_line }}">{{ product.get_extra_attributes_line|truncatewords:10|default:"--" }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans "Color" %}</span>
                            <span class="font-medium text-gray-900">{{ product.color|default:"--" }}</span>
                        </div>
                        <div class="flex justify-between py-2">
                            <span class="text-gray-600">{% trans "Color code" %}</span>
                            <span class="font-medium text-gray-900">{{ product.color_code|default:"--" }}</span>
                        </div>
                    </div>
                    <div class="space-y-3">
                        {% with attr=product.tecpap_attributes.all.0 %}
                        {% with model=attr.model.all.0 %}
                        {% with typ=attr.typ.all.0 %}
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'Manufacturer' %}</span>
                            <span class="font-medium text-gray-900">{{ attr.manufacturer }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'Model' %}</span>
                            <span class="font-medium text-gray-900">{{ model.name }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'Year' %}</span>
                            <span class="font-medium text-gray-900">{{ attr.year }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'Type' %}</span>
                            <span class="font-medium text-gray-900">{{ typ.name|default:'--' }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'kW' %}</span>
                            <span class="font-medium text-gray-900">{{ typ.kw_from|default:'--' }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'hp' %}</span>
                            <span class="font-medium text-gray-900">{{ typ.hp_from|default:'--' }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">{% trans 'cc' %}</span>
                            <span class="font-medium text-gray-900">{{ typ.ccm|default:'--' }}</span>
                        </div>
                        <div class="flex justify-between py-2">
                            <span class="text-gray-600">{% trans 'Fuel' %}</span>
                            <span class="font-medium text-gray-900">{{ typ.fuel|default:'--' }}</span>
                        </div>
                        {% endwith %}
                        {% endwith %}
                        {% endwith %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipping Section (Collapsed by default) -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
            <button @click="shippingOpen = !shippingOpen"
                    class="w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">{% trans 'Shipping' %}</h3>
                <svg :class="shippingOpen ? 'rotate-180' : ''"
                     class="w-5 h-5 text-gray-500 transition-transform"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
            </button>
            <div x-show="shippingOpen"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 class="px-6 py-4 bg-white border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-4">{% trans 'Choose shipping method' %}</h4>
                        {% include 'oscar/basket/partials/shipping.html' with product=product %}
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 mb-4">{% trans 'Trust & Security' %}</h4>
                        <div class="space-y-3">
                            <div class="flex items-center gap-3 text-sm text-gray-600">
                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {% trans 'World wide delivery 2-5 days' %}
                            </div>
                            <div class="flex items-center gap-3 text-sm text-gray-600">
                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"/>
                                </svg>
                                {% trans '15 day return policy' %}
                            </div>
                            <div class="flex items-center gap-3 text-sm text-gray-600">
                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                </svg>
                                {% trans 'All parts are kept in our warehouse' %}
                            </div>
                            {% if request.branch.branch != 'es' and request.branch.branch != 'fr' %}
                            <div class="flex items-center gap-3 text-sm text-gray-600">
                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                                {% trans 'Cash on delivery' %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contacts Section (Collapsed by default) -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
            <button @click="contactsOpen = !contactsOpen"
                    class="w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">{% trans 'Contacts' %}</h3>
                <svg :class="contactsOpen ? 'rotate-180' : ''"
                     class="w-5 h-5 text-gray-500 transition-transform"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
            </button>
            <div x-show="contactsOpen"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 class="px-6 py-4 bg-white border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {% with product.owner.account as vendor %}
                    <div class="space-y-3">
                        {% if request.branch.branch == 'eu' %}
                            {% if product.subowner %}
                                {% with product.subowner.account as manager %}
                                    {% if manager.contact_name %}
                                    <div class="flex items-center gap-3 text-sm">
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                        </svg>
                                        <span class="text-gray-900">{{ manager.contact_name }}</span>
                                    </div>
                                    {% endif %}
                                    {% if vendor.contact_address %}
                                    <div class="flex items-center gap-3 text-sm">
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        </svg>
                                        <span class="text-gray-900">{{ vendor.contact_address }}</span>
                                    </div>
                                    {% endif %}
                                    {% if manager.contact_phone %}
                                    <div class="flex items-center gap-3 text-sm">
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                        </svg>
                                        <span class="text-gray-900">{{ manager.contact_phone }}</span>
                                    </div>
                                    {% endif %}
                                {% endwith %}
                            {% else %}
                                {% if vendor.contact_name %}
                                <div class="flex items-center gap-3 text-sm">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    <span class="text-gray-900">{{ vendor.contact_name }}</span>
                                </div>
                                {% endif %}
                                {% if vendor.contact_address %}
                                <div class="flex items-center gap-3 text-sm">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    <span class="text-gray-900">{{ vendor.contact_address }}</span>
                                </div>
                                {% endif %}
                                {% if vendor.contact_phone %}
                                <div class="flex items-center gap-3 text-sm">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    <span class="text-gray-900">{{ vendor.contact_phone }}</span>
                                </div>
                                {% endif %}
                            {% endif %}
                        {% else %}
                            {% contact_info_for_branch request as contact_info %}
                            <div class="flex items-center gap-3 text-sm">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                <span class="text-gray-900">{{ contact_info.contact_name }}</span>
                            </div>
                            <div class="flex items-center gap-3 text-sm">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <span class="text-gray-900">{{ contact_info.contact_address }}</span>
                            </div>
                            <div class="flex items-center gap-3 text-sm">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                </svg>
                                <span class="text-gray-900">{{ contact_info.contact_phone }}</span>
                            </div>
                        {% endif %}
                    </div>
                    <div class="space-y-3">
                        {% if request.branch.branch == 'eu' %}
                            {% if product.subowner %}
                                {% with product.subowner.account as manager %}
                                    {% if vendor.contact_skype %}
                                    <div class="flex items-center gap-3 text-sm">
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                        </svg>
                                        <span class="text-gray-900">{{ vendor.contact_skype }}</span>
                                    </div>
                                    {% endif %}
                                    {% if vendor.get_contact_info %}
                                    <div class="text-sm text-gray-600">{{ vendor.get_contact_info|safe }}</div>
                                    {% endif %}
                                {% endwith %}
                            {% else %}
                                {% if vendor.contact_skype %}
                                <div class="flex items-center gap-3 text-sm">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                    </svg>
                                    <span class="text-gray-900">{{ vendor.contact_skype }}</span>
                                </div>
                                {% endif %}
                                {% if vendor.get_contact_info %}
                                <div class="text-sm text-gray-600">{{ vendor.get_contact_info|safe }}</div>
                                {% endif %}
                            {% endif %}
                        {% else %}
                            {% contact_info_for_branch request as contact_info %}
                            {% if contact_info.contact_skype %}
                            <div class="flex items-center gap-3 text-sm">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                </svg>
                                <span class="text-gray-900">{{ contact_info.contact_skype }}</span>
                            </div>
                            {% endif %}
                            {% if contact_info.get_contact_info %}
                            <div class="text-sm text-gray-600">{{ contact_info.get_contact_info|safe }}</div>
                            {% endif %}
                        {% endif %}
                        <div class="pt-4">
                            <a href="{% url 'contact-vendor' pk=product.id %}" class="btn-danger">
                                {% trans 'Contact vendor' %}
                            </a>
                        </div>
                    </div>
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>
    {% endblock product_main %}
</div>
{% endblock content %}

{% block extrascripts %}
     <script type="text/javascript">
        var DPD_CASH_COUNTRIES = [{% for country in product.get_dpd_cash_countries %}'{{ country }}'{% if not forloop.last %},{% endif %}{% endfor %}];
    </script>
    {{ block.super }}
{% endblock %}

{% block reviews %}{% endblock %}
