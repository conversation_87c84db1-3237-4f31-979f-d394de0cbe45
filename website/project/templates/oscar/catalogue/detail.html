{% extends "oscar/layout_storefront.html" %}


{% load history_tags %}
{% load custom_currency_filters %}
{% load reviews_tags %}
{% load static %}
{% load custom_product_tags %}
{% load display_tags %}
{% load i18n %}
{% load cache %}
{% load custom_purchase_info_tags %}
{% load contact_info_tags %}
{% load tz %}

{% block title %}
    {{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}
{% endblock %}

{% block description %}
    {{ product.get_meta_description|default:""|striptags }}
{% endblock %}

{% block keywords %}
    {{ product.get_meta_keywords|default:""|striptags }}
{% endblock %}

{% block open_graph %}
{% purchase_info_for_product request product as session %}
<meta property="og:title" content="{{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}" >
<meta property="og:description" content="{{ product.get_meta_description|default:""|striptags }}" >
<meta property="og:site_name" content="{{ site.domain }}" >
<meta property="og:image" content="{{ product.get_absolute_main_image_url }}" >
<meta property="product:price:amount" content="{{ session.price.incl_tax|unlocalize }}" >
<meta property="product:price:currency" content="EUR" >
<meta property="product:availability" content="{% if session.availability.is_available_to_buy %}in stock{% else %}out of stock{% endif %}" >
<meta property="og:product:condition" content="{{ product.get_condition_display }}" >
<meta property="og:url" content="{{ current_site }}" >
<meta property="og:type" content="product" >
{% endblock %}

{% block extrahead %}
    {% purchase_info_for_product request product as session %}
    <script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "Product",
        "name": "{{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}",
        "brand": {"@type": "Brand", "name": "{{ product.get_main_manufacturer }}"},
        "mpn": "{{ product.original_code }}",
        "category": "{{ product.get_main_category }}",
        {% if product.color %}"color": "{{ product.color }}",{% endif %}
        {% if product.weight %}"weight": {"value": "{{ product.weight }}", "unitText": "kg"},{% endif %}
        {% if product.height %}"height": "{{ product.height }} cm",{% endif %}
        {% if product.width %}"width": "{{ product.width }} cm",{% endif %}
        {% if product.length %}"depth": "{{ product.length  }} cm",{% endif %}
        "image": [
            {% for image in product.images.all %}
                "https://{{ request.get_host }}/media/{{ image.original.name|escapejs }}"{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        "description": "{{ product.get_meta_description|default:"" }}",
        "review": {
            "@type": "Review",
            "reviewBody": "{% if product.condition == 'used' %}In good, working condition. This is a used part that has been tested and verified to be functioning properly.{% elif product.condition == 'new' %}Brand new part in original condition.{% elif product.condition == 'broken' %}Part is damaged or not functioning, suitable for repairs or parts.{% endif %}",
            "author": {
                "@type": "Person",
                "name": "Quality Control Specialist"
            },
            "reviewRating": {
                "@type": "Rating",
                "ratingValue": "{% if product.condition == 'new' %}5{% elif product.condition == 'used' %}4{% else %}2{% endif %}",
                "bestRating": "5",
                "worstRating": "1"
            }
        },
        "offers": {
            "@type": "Offer",
            "url": "https://{{ request.get_host }}{{ product.get_absolute_url }}",
            "priceCurrency": "EUR",
            "price": "{{ session.price.incl_tax|unlocalize }}",
            "priceValidUntil": "{% now 'Y' as current_year %}{{ current_year|add:'1' }}-{% now 'm-d' %}",
            {% if product.condition == 'used' %}
            "itemCondition": "http://schema.org/UsedCondition",
            {% elif product.condition == 'new' %}
            "itemCondition": "http://schema.org/NewCondition",
            {% elif product.condition == 'broken' %}
            "itemCondition": "http://schema.org/DamagedCondition",
            {% endif %}
            "availability": "{% if session.availability.is_available_to_buy %}http://schema.org/InStock{% else %}http://schema.org/OutOfStock{% endif %}",
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                {% if request.LANGUAGE_CODE == 'de' and request.branch.branch == 'de' %}
                "returnPolicyCountry": "DE",
                "applicableCountry": "DE",
                {% elif request.LANGUAGE_CODE == 'pl' and request.branch.branch == 'pl' %}
                "returnPolicyCountry": "PL",
                "applicableCountry": "PL",
                {% elif request.LANGUAGE_CODE == 'lt' and request.branch.branch == 'lt' %}
                "returnPolicyCountry": "LT",
                "applicableCountry": "LT",
                {% elif request.LANGUAGE_CODE == 'fr' and request.branch.branch == 'fr' %}
                "returnPolicyCountry": "FR",
                "applicableCountry": "FR",
                {% elif request.LANGUAGE_CODE == 'es' and request.branch.branch == 'es' %}
                "returnPolicyCountry": "ES",
                "applicableCountry": "ES",
                {% endif %}
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "merchantReturnDays": 14,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/ReturnFeesCustomerResponsibility"
            }
        }
    }
    </script>
    {{ block.super }}
{% endblock extrahead %}

{% block header %}{% endblock %}

{% block breadcrumbs %}
<ul class="breadcrumb">
    <li>
        <a href="{% url 'promotions:home' %}">{% trans "Home" %}</a>
    </li>
    {% with category=product.categories.all.0 %}
        {% for c in category.get_ancestors %}
        <li>
            <a href="{{ c.get_absolute_url }}">{{ c.get_name }}</a>
        </li>
        {% endfor %}
        <li class="active">{{ product.get_title }}</li>
    {% endwith %}
</ul>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

        {% block product_gallery %}
        <!-- Product Gallery -->
        <div class="space-y-4">
            {% cache 500 product_galery LANGUAGE_CODE product.id request.branch %}
                <div class="bg-white rounded-lg overflow-hidden">
                    {% with all_images=product.images.all %}
                    {% if all_images|length > 1 %}
                        <!-- Main Image -->
                        <div class="aspect-square bg-gray-50 flex items-center justify-center">
                            {% with image=product.primary_image %}
                                {% oscar_thumbnail image.original "500x500" upscale=False crop="center" as thumb %}
                                <img src="{{ thumb.url }}" alt="{{ product.get_title }}"
                                     class="max-w-full max-h-full object-contain">
                            {% endwith %}
                        </div>

                        <!-- Thumbnail Gallery -->
                        <div class="flex gap-2 p-4 overflow-x-auto">
                            {% for image in all_images %}
                                {% oscar_thumbnail image.original "80x80" crop="center" as thumb %}
                                <button class="flex-shrink-0 w-16 h-16 border-2 border-gray-200 rounded-lg overflow-hidden hover:border-blue-500 transition-colors">
                                    <img src="{{ thumb.url }}" alt="{{ product.get_title }} - {{ forloop.counter }}"
                                         class="w-full h-full object-cover">
                                </button>
                            {% endfor %}
                        </div>
                    {% else %}
                        <!-- Single Image -->
                        <div class="aspect-square bg-gray-50 flex items-center justify-center">
                            {% with image=product.primary_image %}
                                {% oscar_thumbnail image.original "500x500" upscale=False crop="center" as thumb %}
                                <img src="{{ thumb.url }}" alt="{{ product.get_title }}"
                                     class="max-w-full max-h-full object-contain">
                            {% endwith %}
                        </div>
                    {% endif %}
                    {% endwith %}
                </div>
            {% endcache %}

            <!-- Product Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p class="text-sm text-blue-800">
                    {% trans 'Before buying this item please make sure if this part actually fits. Pay attention to the part number in the description and picture to match your parts number.' %}
                </p>
            </div>
        </div>
        {% endblock product_gallery %}

        {% block product_main %}
        <!-- Product Information -->
        <div class="space-y-6">
            <!-- Product Title -->
            <div>
                <h1 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                    {{ product.get_title }}
                </h1>
            </div>

            <!-- Price and Rating -->
            {% purchase_info_for_product request product as session %}
            {% discount_info_for_product request product as discount_info %}
            <div class="space-y-4">
                <div class="flex items-center gap-4">
                    <div class="text-3xl font-bold text-gray-900">
                        {% if session.price.exists and session.price.is_tax_known %}
                            {{ session.price.incl_tax|user_currency:user_currency }}
                        {% endif %}
                    </div>
                    {% if discount_info.has_discount %}
                        <div class="text-xl text-gray-500 line-through">
                            {{ discount_info.old_price_incl_tax|user_currency:user_currency }}
                        </div>
                    {% endif %}
                </div>

                <!-- Rating Stars (placeholder) -->
                <div class="flex items-center gap-2">
                    <div class="flex text-blue-500">
                        {% for i in "12345" %}
                            <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20">
                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                            </svg>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Product Description -->
            <div class="prose prose-sm text-gray-600">
                <p>{{ product.get_meta_description|default:""|striptags }}</p>
            </div>

            <!-- Color Selection (if applicable) -->
            {% if product.color %}
            <div class="space-y-3">
                <h3 class="text-sm font-medium text-gray-900">{% trans "Color" %}</h3>
                <div class="flex gap-2">
                    <button class="w-8 h-8 rounded-full border-2 border-gray-300 bg-gray-800 ring-2 ring-blue-500 ring-offset-2"></button>
                    <button class="w-8 h-8 rounded-full border-2 border-gray-300 bg-blue-600"></button>
                </div>
            </div>
            {% endif %}

            {% if session.availability.is_available_to_buy %}
            <!-- Add to Cart Form -->
            <form action="{% url 'basket:add' pk=product.pk %}" method="post" class="space-y-6">
                {% csrf_token %}
                <input id="id_product_id" name="product_id" value="{{ product.id }}" type="hidden">

                <!-- Quantity and Add to Cart -->
                <div class="flex gap-4">
                    <div class="flex items-center border border-gray-300 rounded-lg">
                        <button type="button" class="px-3 py-2 text-gray-500 hover:text-gray-700 js-qntDecrease">-</button>
                        <input type="text" name="quantity" id="id_quantity" value="1"
                               class="w-16 text-center border-0 focus:ring-0 focus:outline-none"
                               aria-label="{% trans 'Quantity' %}">
                        <button type="button" class="px-3 py-2 text-gray-500 hover:text-gray-700 js-qntIncrease">+</button>
                    </div>

                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors">
                        {% trans 'Add to bag' %}
                    </button>

                    <button type="button" class="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                    </button>
                </div>

                <!-- Stock Info -->
                <div class="text-sm text-gray-600">
                    {% trans 'Stock' %}: {{ session.stockrecord.num_in_stock }}
                </div>

                <!-- Offer Price Link -->
                {% if product.active %}
                <div class="pt-2">
                    <a href="{% url 'price-offer' pk=product.pk %}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        {% trans 'Offer your price' %}
                    </a>
                </div>
                {% endif %}
            </form>
            {% endif %}

        <div class="row pb-4">
            <div class="col-12 col-sm-6 pb-4 pb-sm-1">

                <div class="side_block">
                    <h3>{% trans 'Choose shipping method' %}</h3>
                        {% include 'oscar/basket/partials/shipping.html' with product=product %}
                </div>
            </div>
            <div class="col-12 col-sm-6">
                <ul class="trust_block">
                    <li>
                        <img src="{{ STATIC_URL }}img/ico_ww_shiping.png" alt="Worldwide shipping icon" >{% trans 'World wide delivery 2-5 days' %}
                    </li>
                    <li>
                        <img src="{{ STATIC_URL }}img/ico_return.png" alt="Return policy icon" >{% trans '15 day return policy' %}
                    </li>
                    <li>
                        <img src="{{ STATIC_URL }}img/ico_warehouse.png" alt="Warehouse icon" >{% trans 'All parts are kept in our warehouse' %}
                    </li>
                    {% if request.branch.branch != 'es' and request.branch.branch != 'fr' %}
                    <li>
                        <img src="{{ STATIC_URL }}img/ico_cashondelivery.png" alt="Cash on delivery icon" >{% trans 'Cash on delivery' %}
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="row pb-4 content_tabs">
            <ul class="nav nav-tabs col-12" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" id="product-information-tab" data-toggle="tab" href="#product-information" role="tab" aria-controls="product-information" aria-selected="true">{% trans 'Details' %}</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="contact-information-tab" data-toggle="tab" href="#contact-information" role="tab" aria-controls="contact-information" aria-selected="false">{% trans 'Contacts' %}</a>
                </li>
            </ul>
            <div class="tab-content col-12">
                <div class="tab-pane fade show active" id="product-information" role="tabpanel" aria-labelledby="product-information-tab">
                    <div class="row">
                        <div class="col-12 col-sm-6">
                            <p>{% trans 'Name' %} <span class="float-right">{{ product.get_main_category|safe }}</span></p>
                            <p>{% trans "Commercial ID" %} <span class="float-right">{{ product.commercial_id }}</span></p>
                            <p>{% trans "Product code (MPN)" %} <span class="float-right" title="{{ product.original_code }}">{{ product.original_code|truncatewords:4|default:"--" }}</span></p>
                            {% purchase_info_for_product request product as session %}
                            {% if session.price.exists and session.price.is_tax_known %}
                            <p>{% trans "Price (incl. tax)" %} <span class="float-right">{{ session.price.incl_tax|user_currency:user_currency }}</span></p>
                            {% endif %}
                            <p>{% trans "Condition" %} <span class="float-right"><a href="javascript:void(0)" class="jsToggleExplainCondition">{{ product.get_condition_display }}</a></span></p>
                            <div class="jsExplainCondition text-center" style="display: none">
                                <div class="row">
                                    <div class="col-12">
                                        <p class="product_notice">{% if product.condition == 'new' %}{{ product.explain_condition_new }}{% else %}{{ product.explain_condition_used }}{% endif %}</p>
                                    </div>
                                </div>
                            </div>
                            <p>{% trans 'Drive position' %} <span class="float-right">{{ product.get_drive_position_display|default:"--" }}</span></p>
                            <p>{% trans 'Attributes' %} <span class="float-right" title="{{ product.get_extra_attributes_line }}">{{ product.get_extra_attributes_line|truncatewords:10|default:"--" }}</span></p>
                            <p>{% trans "Color" %} <span class="float-right">{{ product.color|default:"--" }}</span></p>
                            <p>{% trans "Color code" %} <span class="float-right">{{ product.color_code|default:"--" }}</span></p>
                        </div>
                        <div class="col-12 col-sm-6">
                            {% with attr=product.tecpap_attributes.all.0 %}
                            {% with model=attr.model.all.0 %}
                            {% with typ=attr.typ.all.0 %}
                            <p>{% trans 'Manufacturer' %} <span class="float-right">{{ attr.manufacturer }}</span></p>
                            <p>{% trans 'Model' %} <span class="float-right">{{ model.name }}</span></p>
                            <p>{% trans 'Year' %}<span class="float-right">{{ attr.year }}</span></p>
                            <p>{% trans 'Type' %}<span class="float-right">{{ typ.name|default:'--' }}</span></p>
                            <p>{% trans 'kW' %}<span class="float-right">{{ typ.kw_from|default:'--' }}</span></p>
                            <p>{% trans 'hp' %}<span class="float-right">{{ typ.hp_from|default:'--' }}</span></p>
                            <p>{% trans 'cc' %}<span class="float-right">{{ typ.ccm|default:'--' }}</span></p>
                            <p>{% trans 'Fuel' %}<span class="float-right">{{ typ.fuel|default:'--' }}</span></p>
                            {% endwith %}
                            {% endwith %}
                            {% endwith %}
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="contact-information" role="tabpanel" aria-labelledby="contact-information-tab">
                    <div class="row">
                        {% with product.owner.account as vendor %}
                        <div class="col-12 col-sm-6">
                            {% if request.branch.branch == 'eu' %}
                                {% if product.subowner %}
                                    {% with product.subowner.account as manager %}
                                        {% if manager.contact_name %}<p><span class="contacts icon-user"></span>{{ manager.contact_name }}</p>{% endif %}
                                        {% if vendor.contact_address %}<p><span class="contacts google-mapsai"></span>{{ vendor.contact_address }}</p>{% endif %}
                                        {% if manager.contact_phone %}<p><span class="contacts icon-phone"></span>{{ manager.contact_phone }}</p>{% endif %}
                                    {% endwith %}
                                {% else %}
                                    {% if vendor.contact_name %}<p><span class="contacts icon-user"></span>{{ vendor.contact_name }}</p>{% endif %}
                                    {% if vendor.contact_address %}<p><span class="contacts google-mapsai"></span>{{ vendor.contact_address }}</p>{% endif %}
                                    {% if vendor.contact_phone %}<p><span class="contacts icon-phone"></span>{{ vendor.contact_phone }}</p>{% endif %}
                                {% endif %}
                            {% else %}
                                {% contact_info_for_branch request as contact_info %}
                                <p><span class="contacts icon-user"></span>{{ contact_info.contact_name }}</p>
                                <p><span class="contacts google-mapsai"></span>{{ contact_info.contact_address }}</p>
                                <p><span class="contacts icon-phone"></span>{{ contact_info.contact_phone }}</p>
                            {% endif %}
                        </div>
                        <div class="col-12 col-sm-6">
                            {% if request.branch.branch == 'eu' %}
                                {% if product.subowner %}
                                    {% with product.subowner.account as manager %}
                                        {% if vendor.contact_skype %}<p><span class="contacts skype"></span>{{ vendor.contact_skype }}</p>{% endif %}
                                        {% if vendor.get_contact_info %}<p><span class="contacts icon-user"></span>{{ vendor.get_contact_info|safe}}</p>{% endif %}
                                    {% endwith %}
                                {% else %}
                                    {% if vendor.contact_skype %}<p><span class="contacts skype"></span>{{ vendor.contact_skype }}</p>{% endif %}
                                    {% if vendor.get_contact_info %}<p><span class="contacts icon-user"></span>{{ vendor.get_contact_info|safe}}</p>{% endif %}
                                {% endif %}
                            {% else %}
                                {% contact_info_for_branch request as contact_info %}
                                {% if contact_info.contact_skype %}<p><span class="contacts skype"></span>{{ contact_info.contact_skype }}</p>{% endif %}
                                {% if contact_info.get_contact_info %}<p><span class="contacts icon-user"></span>{{ contact_info.get_contact_info|safe}}</p>{% endif %}
                            {% endif %}
                            <p><a href="{% url 'contact-vendor' pk=product.id %}" class="btn btn-danger">{% trans 'Contact vendor' %}</a></p>
                        </div>
                        {% endwith %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endblock product_main %}
</div>
{% endblock content %}

{% block extrascripts %}
     <script type="text/javascript">
        var DPD_CASH_COUNTRIES = [{% for country in product.get_dpd_cash_countries %}'{{ country }}'{% if not forloop.last %},{% endif %}{% endfor %}];
    </script>
    {{ block.super }}
{% endblock %}

{% block reviews %}{% endblock %}
