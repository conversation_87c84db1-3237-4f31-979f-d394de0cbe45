{% extends "oscar/layout_storefront.html" %}


{% load history_tags %}
{% load custom_currency_filters %}
{% load reviews_tags %}
{% load static %}
{% load custom_product_tags %}
{% load display_tags %}
{% load i18n %}
{% load cache %}
{% load custom_purchase_info_tags %}
{% load contact_info_tags %}
{% load tz %}

{% block title %}
    {{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}
{% endblock %}

{% block description %}
    {{ product.get_meta_description|default:""|striptags }}
{% endblock %}

{% block keywords %}
    {{ product.get_meta_keywords|default:""|striptags }}
{% endblock %}

{% block open_graph %}
{% purchase_info_for_product request product as session %}
<meta property="og:title" content="{{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}" >
<meta property="og:description" content="{{ product.get_meta_description|default:""|striptags }}" >
<meta property="og:site_name" content="{{ site.domain }}" >
<meta property="og:image" content="{{ product.get_absolute_main_image_url }}" >
<meta property="product:price:amount" content="{{ session.price.incl_tax|unlocalize }}" >
<meta property="product:price:currency" content="EUR" >
<meta property="product:availability" content="{% if session.availability.is_available_to_buy %}in stock{% else %}out of stock{% endif %}" >
<meta property="og:product:condition" content="{{ product.get_condition_display }}" >
<meta property="og:url" content="{{ current_site }}" >
<meta property="og:type" content="product" >
{% endblock %}

{% block extrahead %}
    {% purchase_info_for_product request product as session %}
    <script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "Product",
        "name": "{{ product.get_main_category }} {{ product.original_code_cleaned }} {{ product.get_main_manufacturer }} {{ product.get_main_model_cleaned }} {{ product.get_main_year }}",
        "brand": {"@type": "Brand", "name": "{{ product.get_main_manufacturer }}"},
        "mpn": "{{ product.original_code }}",
        "category": "{{ product.get_main_category }}",
        {% if product.color %}"color": "{{ product.color }}",{% endif %}
        {% if product.weight %}"weight": {"value": "{{ product.weight }}", "unitText": "kg"},{% endif %}
        {% if product.height %}"height": "{{ product.height }} cm",{% endif %}
        {% if product.width %}"width": "{{ product.width }} cm",{% endif %}
        {% if product.length %}"depth": "{{ product.length  }} cm",{% endif %}
        "image": [
            {% for image in product.images.all %}
                "https://{{ request.get_host }}/media/{{ image.original.name|escapejs }}"{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        "description": "{{ product.get_meta_description|default:"" }}",
        "review": {
            "@type": "Review",
            "reviewBody": "{% if product.condition == 'used' %}In good, working condition. This is a used part that has been tested and verified to be functioning properly.{% elif product.condition == 'new' %}Brand new part in original condition.{% elif product.condition == 'broken' %}Part is damaged or not functioning, suitable for repairs or parts.{% endif %}",
            "author": {
                "@type": "Person",
                "name": "Quality Control Specialist"
            },
            "reviewRating": {
                "@type": "Rating",
                "ratingValue": "{% if product.condition == 'new' %}5{% elif product.condition == 'used' %}4{% else %}2{% endif %}",
                "bestRating": "5",
                "worstRating": "1"
            }
        },
        "offers": {
            "@type": "Offer",
            "url": "https://{{ request.get_host }}{{ product.get_absolute_url }}",
            "priceCurrency": "EUR",
            "price": "{{ session.price.incl_tax|unlocalize }}",
            "priceValidUntil": "{% now 'Y' as current_year %}{{ current_year|add:'1' }}-{% now 'm-d' %}",
            {% if product.condition == 'used' %}
            "itemCondition": "http://schema.org/UsedCondition",
            {% elif product.condition == 'new' %}
            "itemCondition": "http://schema.org/NewCondition",
            {% elif product.condition == 'broken' %}
            "itemCondition": "http://schema.org/DamagedCondition",
            {% endif %} 
            "availability": "{% if session.availability.is_available_to_buy %}http://schema.org/InStock{% else %}http://schema.org/OutOfStock{% endif %}",
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                {% if request.LANGUAGE_CODE == 'de' and request.branch.branch == 'de' %}
                "returnPolicyCountry": "DE",
                "applicableCountry": "DE",
                {% elif request.LANGUAGE_CODE == 'pl' and request.branch.branch == 'pl' %}
                "returnPolicyCountry": "PL", 
                "applicableCountry": "PL",
                {% elif request.LANGUAGE_CODE == 'lt' and request.branch.branch == 'lt' %}
                "returnPolicyCountry": "LT",
                "applicableCountry": "LT",
                {% elif request.LANGUAGE_CODE == 'fr' and request.branch.branch == 'fr' %}
                "returnPolicyCountry": "FR",
                "applicableCountry": "FR",
                {% elif request.LANGUAGE_CODE == 'es' and request.branch.branch == 'es' %}
                "returnPolicyCountry": "ES",
                "applicableCountry": "ES",
                {% endif %}
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "merchantReturnDays": 14,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/ReturnFeesCustomerResponsibility"
            }
        }
    }
    </script>
    {{ block.super }}
{% endblock extrahead %}

{% block header %}{% endblock %}

{% block breadcrumbs %}
<ul class="breadcrumb">
    <li>
        <a href="{% url 'promotions:home' %}">{% trans "Home" %}</a>
    </li>
    {% with category=product.categories.all.0 %}
        {% for c in category.get_ancestors %}
        <li>
            <a href="{{ c.get_absolute_url }}">{{ c.get_name }}</a>
        </li>
        {% endfor %}
        <li class="active">{{ product.get_title }}</li>
    {% endwith %}
</ul>
{% endblock %}

{% block content %}
<div class="row product_page"><!-- Start of product page -->
    {% block product_gallery %}
    <div class="col-12 col-sm-4">
        {% cache 500 product_galery LANGUAGE_CODE product.id request.branch %}
            {% include "oscar/catalogue/partials/gallery.html" %}
        {% endcache %}

        <div class="product_notice pb-4 pb-sm-1">
            <p>
                {% trans 'Before buying this item please make sure if this part actually fits. Pay attention to the part number in the description and picture to match your parts number.' %}
            </p>
        </div>
    </div>
    {% endblock product_gallery %}

    {% block product_main %}
    <div class="col-12 col-sm-8">
        <h1 class="product_item_title d-block pb-4">
            {{ product.get_title }}
        </h1>

        {% block product_stock_record_and_basket_form %}
        {% purchase_info_for_product request product as session %}
        {% discount_info_for_product request product as discount_info %}
        {% if session.availability.is_available_to_buy %}
        <form action="{% url 'basket:add' pk=product.pk %}" method="post" class="form-stacked add-to-basket">
            {% csrf_token %}
            <input id="id_product_id" name="product_id" value="{{ product.id }}" type="hidden">
            <div class="price d-block pb-2 clearfix">
                <div class="price_display">{% include "oscar/catalogue/partials/stock_record.html" with verbose=1 user_currency=user_currency %}</div>                
                <div class="qty_input">
                    <input type="text" name="quantity" id="id_quantity" value="1" class="qty nofocus" aria-label="{% trans 'Quantity' %}">
                    <div class="qty_controls">
                        <span class="qty_increase js-qntIncrease" title="Increase quantity">&nbsp;</span>
                        <span class="qty_decrease js-qntDecrease" title="Decrease quantity">&nbsp;</span>
                    </div>
                </div>
                <div class="item_condition">
                    {% trans 'Stock' %}: {{ session.stockrecord.num_in_stock }}
                </div>
            </div>

            {% if discount_info.has_discount  %}
            <div class="row">
                <div class="col-12 col-sm-12">
                    <strike><h5>{{ discount_info.old_price_incl_tax|user_currency:user_currency }}</h5></strike>
                </div>
            </div>
            {% endif %}

            <div class="row pb-4">
                <div class="col-12 col-sm-6 pb-4 pb-sm-1">
                    {% if product.active %}
                    <div class="buy_item_container">
                        <button type="submit" class="buy_item nofocus">{% trans 'Buy now' %}</button>
                        <br>
                        <br>
                        <a href="{% url 'price-offer' pk=product.pk %}">
                            {% trans 'Offer your price' %}
                        </a>
                    </div>
                    {% endif %}
                </div>
                <div class="col-12 col-sm-6">
                    <div class="order_faster">
                        {% if time_left_for_today_shipping %}
                        <p>
                            <img src="{{ STATIC_URL }}img/ico_order_now_clock.png" alt="Order now clock icon">
                            {% trans 'Place order in coming' %} <span class="partan_red js-countDown" data-start="{{ time_left_for_today_shipping }}">01:08:45</span> {% trans 'and we will send out your order today!' %}
                        </p>
                       {% endif %}
                    </div>
                </div>
            </div>
        </form>
        {% endif %}
        {% endblock %}

        <div class="row pb-4">
            <div class="col-12 col-sm-6 pb-4 pb-sm-1">
                
                <div class="side_block">
                    <h3>{% trans 'Choose shipping method' %}</h3>
                        {% include 'oscar/basket/partials/shipping.html' with product=product %}
                </div>
            </div>
            <div class="col-12 col-sm-6">
                <ul class="trust_block">
                    <li>
                        <img src="{{ STATIC_URL }}img/ico_ww_shiping.png" alt="Worldwide shipping icon" >{% trans 'World wide delivery 2-5 days' %}
                    </li>
                    <li>
                        <img src="{{ STATIC_URL }}img/ico_return.png" alt="Return policy icon" >{% trans '15 day return policy' %}
                    </li>
                    <li>
                        <img src="{{ STATIC_URL }}img/ico_warehouse.png" alt="Warehouse icon" >{% trans 'All parts are kept in our warehouse' %}
                    </li>
                    {% if request.branch.branch != 'es' and request.branch.branch != 'fr' %}
                    <li>
                        <img src="{{ STATIC_URL }}img/ico_cashondelivery.png" alt="Cash on delivery icon" >{% trans 'Cash on delivery' %}
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="row pb-4 content_tabs">
            <ul class="nav nav-tabs col-12" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" id="product-information-tab" data-toggle="tab" href="#product-information" role="tab" aria-controls="product-information" aria-selected="true">{% trans 'Details' %}</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="contact-information-tab" data-toggle="tab" href="#contact-information" role="tab" aria-controls="contact-information" aria-selected="false">{% trans 'Contacts' %}</a>
                </li>
            </ul>
            <div class="tab-content col-12">
                <div class="tab-pane fade show active" id="product-information" role="tabpanel" aria-labelledby="product-information-tab">
                    <div class="row">
                        <div class="col-12 col-sm-6">
                            <p>{% trans 'Name' %} <span class="float-right">{{ product.get_main_category|safe }}</span></p>
                            <p>{% trans "Commercial ID" %} <span class="float-right">{{ product.commercial_id }}</span></p>
                            <p>{% trans "Product code (MPN)" %} <span class="float-right" title="{{ product.original_code }}">{{ product.original_code|truncatewords:4|default:"--" }}</span></p>
                            {% purchase_info_for_product request product as session %}
                            {% if session.price.exists and session.price.is_tax_known %}
                            <p>{% trans "Price (incl. tax)" %} <span class="float-right">{{ session.price.incl_tax|user_currency:user_currency }}</span></p>
                            {% endif %}
                            <p>{% trans "Condition" %} <span class="float-right"><a href="javascript:void(0)" class="jsToggleExplainCondition">{{ product.get_condition_display }}</a></span></p>
                            <div class="jsExplainCondition text-center" style="display: none">
                                <div class="row">
                                    <div class="col-12">
                                        <p class="product_notice">{% if product.condition == 'new' %}{{ product.explain_condition_new }}{% else %}{{ product.explain_condition_used }}{% endif %}</p>
                                    </div>
                                </div>
                            </div>
                            <p>{% trans 'Drive position' %} <span class="float-right">{{ product.get_drive_position_display|default:"--" }}</span></p>
                            <p>{% trans 'Attributes' %} <span class="float-right" title="{{ product.get_extra_attributes_line }}">{{ product.get_extra_attributes_line|truncatewords:10|default:"--" }}</span></p>
                            <p>{% trans "Color" %} <span class="float-right">{{ product.color|default:"--" }}</span></p>
                            <p>{% trans "Color code" %} <span class="float-right">{{ product.color_code|default:"--" }}</span></p>
                        </div>
                        <div class="col-12 col-sm-6">
                            {% with attr=product.tecpap_attributes.all.0 %}
                            {% with model=attr.model.all.0 %}
                            {% with typ=attr.typ.all.0 %}
                            <p>{% trans 'Manufacturer' %} <span class="float-right">{{ attr.manufacturer }}</span></p>
                            <p>{% trans 'Model' %} <span class="float-right">{{ model.name }}</span></p>
                            <p>{% trans 'Year' %}<span class="float-right">{{ attr.year }}</span></p>
                            <p>{% trans 'Type' %}<span class="float-right">{{ typ.name|default:'--' }}</span></p>
                            <p>{% trans 'kW' %}<span class="float-right">{{ typ.kw_from|default:'--' }}</span></p>
                            <p>{% trans 'hp' %}<span class="float-right">{{ typ.hp_from|default:'--' }}</span></p>
                            <p>{% trans 'cc' %}<span class="float-right">{{ typ.ccm|default:'--' }}</span></p>
                            <p>{% trans 'Fuel' %}<span class="float-right">{{ typ.fuel|default:'--' }}</span></p>
                            {% endwith %}
                            {% endwith %}
                            {% endwith %}
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="contact-information" role="tabpanel" aria-labelledby="contact-information-tab">
                    <div class="row">
                        {% with product.owner.account as vendor %}
                        <div class="col-12 col-sm-6">
                            {% if request.branch.branch == 'eu' %}
                                {% if product.subowner %}
                                    {% with product.subowner.account as manager %}
                                        {% if manager.contact_name %}<p><span class="contacts icon-user"></span>{{ manager.contact_name }}</p>{% endif %}
                                        {% if vendor.contact_address %}<p><span class="contacts google-mapsai"></span>{{ vendor.contact_address }}</p>{% endif %}
                                        {% if manager.contact_phone %}<p><span class="contacts icon-phone"></span>{{ manager.contact_phone }}</p>{% endif %}
                                    {% endwith %}
                                {% else %}
                                    {% if vendor.contact_name %}<p><span class="contacts icon-user"></span>{{ vendor.contact_name }}</p>{% endif %}
                                    {% if vendor.contact_address %}<p><span class="contacts google-mapsai"></span>{{ vendor.contact_address }}</p>{% endif %}
                                    {% if vendor.contact_phone %}<p><span class="contacts icon-phone"></span>{{ vendor.contact_phone }}</p>{% endif %}
                                {% endif %}
                            {% else %}
                                {% contact_info_for_branch request as contact_info %}
                                <p><span class="contacts icon-user"></span>{{ contact_info.contact_name }}</p>
                                <p><span class="contacts google-mapsai"></span>{{ contact_info.contact_address }}</p>
                                <p><span class="contacts icon-phone"></span>{{ contact_info.contact_phone }}</p>
                            {% endif %}
                        </div>
                        <div class="col-12 col-sm-6">
                            {% if request.branch.branch == 'eu' %}
                                {% if product.subowner %}
                                    {% with product.subowner.account as manager %}
                                        {% if vendor.contact_skype %}<p><span class="contacts skype"></span>{{ vendor.contact_skype }}</p>{% endif %}
                                        {% if vendor.get_contact_info %}<p><span class="contacts icon-user"></span>{{ vendor.get_contact_info|safe}}</p>{% endif %}
                                    {% endwith %}
                                {% else %}
                                    {% if vendor.contact_skype %}<p><span class="contacts skype"></span>{{ vendor.contact_skype }}</p>{% endif %}
                                    {% if vendor.get_contact_info %}<p><span class="contacts icon-user"></span>{{ vendor.get_contact_info|safe}}</p>{% endif %}
                                {% endif %}
                            {% else %}
                                {% contact_info_for_branch request as contact_info %}
                                {% if contact_info.contact_skype %}<p><span class="contacts skype"></span>{{ contact_info.contact_skype }}</p>{% endif %}
                                {% if contact_info.get_contact_info %}<p><span class="contacts icon-user"></span>{{ contact_info.get_contact_info|safe}}</p>{% endif %}
                            {% endif %}
                            <p><a href="{% url 'contact-vendor' pk=product.id %}" class="btn btn-danger">{% trans 'Contact vendor' %}</a></p>
                        </div>
                        {% endwith %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endblock product_main %}
</div>
{% endblock content %}

{% block extrascripts %}
     <script type="text/javascript">
        var DPD_CASH_COUNTRIES = [{% for country in product.get_dpd_cash_countries %}'{{ country }}'{% if not forloop.last %},{% endif %}{% endfor %}];
    </script>
    {{ block.super }}
{% endblock %}

{% block reviews %}{% endblock %}
