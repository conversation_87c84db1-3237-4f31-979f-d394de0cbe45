/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: 'Montser<PERSON>', 'Montserrat Fallback', 'Segoe UI', '<PERSON><PERSON>', 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
    monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-yellow-50: oklch(98.7% 0.026 102.212);
    --color-yellow-100: oklch(97.3% 0.071 103.193);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-700: oklch(55.4% 0.135 66.442);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-700: oklch(52.7% 0.154 150.069);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-blue-950: oklch(28.2% 0.091 267.935);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --breakpoint-sm: 40rem;
    --breakpoint-md: 48rem;
    --breakpoint-lg: 64rem;
    --breakpoint-xl: 80rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wider: 0.05em;
    --leading-relaxed: 1.625;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --font-montserrat: 'Montserrat', 'Montserrat Fallback', 'Segoe UI', 'Roboto', 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji');
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type='button'], [type='reset'], [type='submit']), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden='until-found'])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-full {
    top: 100%;
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1\.5 {
    right: calc(var(--spacing) * 1.5);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-10 {
    right: calc(var(--spacing) * 10);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .z-10 {
    z-index: 10;
  }
  .z-50 {
    z-index: 50;
  }
  .z-100 {
    z-index: 100;
  }
  .order-0 {
    order: 0;
  }
  .order-1 {
    order: 1;
  }
  .order-2 {
    order: 2;
  }
  .order-3 {
    order: 3;
  }
  .order-4 {
    order: 4;
  }
  .order-5 {
    order: 5;
  }
  .order-6 {
    order: 6;
  }
  .order-7 {
    order: 7;
  }
  .order-8 {
    order: 8;
  }
  .order-9 {
    order: 9;
  }
  .order-10 {
    order: 10;
  }
  .order-11 {
    order: 11;
  }
  .order-12 {
    order: 12;
  }
  .order-first {
    order: -9999;
  }
  .order-last {
    order: 9999;
  }
  .col-1 {
    grid-column: 1;
  }
  .col-2 {
    grid-column: 2;
  }
  .col-3 {
    grid-column: 3;
  }
  .col-4 {
    grid-column: 4;
  }
  .col-5 {
    grid-column: 5;
  }
  .col-6 {
    grid-column: 6;
  }
  .col-7 {
    grid-column: 7;
  }
  .col-8 {
    grid-column: 8;
  }
  .col-9 {
    grid-column: 9;
  }
  .col-10 {
    grid-column: 10;
  }
  .col-11 {
    grid-column: 11;
  }
  .col-12 {
    grid-column: 12;
  }
  .col-auto {
    grid-column: auto;
  }
  .float-left {
    float: left;
  }
  .float-none {
    float: none;
  }
  .float-right {
    float: right;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .m-1 {
    margin: calc(var(--spacing) * 1);
  }
  .m-2 {
    margin: calc(var(--spacing) * 2);
  }
  .m-3 {
    margin: calc(var(--spacing) * 3);
  }
  .m-4 {
    margin: calc(var(--spacing) * 4);
  }
  .m-5 {
    margin: calc(var(--spacing) * 5);
  }
  .m-auto {
    margin: auto;
  }
  .-mx-2 {
    margin-inline: calc(var(--spacing) * -2);
  }
  .mx-0 {
    margin-inline: calc(var(--spacing) * 0);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-auto {
    margin-block: auto;
  }
  .me-2 {
    margin-inline-end: calc(var(--spacing) * 2);
  }
  .prose {
    color: var(--tw-prose-body);
    max-width: 65ch;
    :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-lead);
      font-size: 1.25em;
      line-height: 1.6;
      margin-top: 1.2em;
      margin-bottom: 1.2em;
    }
    :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-links);
      text-decoration: underline;
      font-weight: 500;
    }
    :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-bold);
      font-weight: 600;
    }
    :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
    }
    :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: disc;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      font-weight: 400;
      color: var(--tw-prose-counters);
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      color: var(--tw-prose-bullets);
    }
    :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.25em;
    }
    :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-color: var(--tw-prose-hr);
      border-top-width: 1;
      margin-top: 3em;
      margin-bottom: 3em;
    }
    :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 500;
      font-style: italic;
      color: var(--tw-prose-quotes);
      border-inline-start-width: 0.25rem;
      border-inline-start-color: var(--tw-prose-quote-borders);
      quotes: "\201C""\201D""\2018""\2019";
      margin-top: 1.6em;
      margin-bottom: 1.6em;
      padding-inline-start: 1em;
    }
    :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: open-quote;
    }
    :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: close-quote;
    }
    :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 800;
      font-size: 2.25em;
      margin-top: 0;
      margin-bottom: 0.8888889em;
      line-height: 1.1111111;
    }
    :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 900;
      color: inherit;
    }
    :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 700;
      font-size: 1.5em;
      margin-top: 2em;
      margin-bottom: 1em;
      line-height: 1.3333333;
    }
    :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 800;
      color: inherit;
    }
    :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      font-size: 1.25em;
      margin-top: 1.6em;
      margin-bottom: 0.6em;
      line-height: 1.6;
    }
    :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
      line-height: 1.5;
    }
    :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      display: block;
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 500;
      font-family: inherit;
      color: var(--tw-prose-kbd);
      box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
      font-size: 0.875em;
      border-radius: 0.3125rem;
      padding-top: 0.1875em;
      padding-inline-end: 0.375em;
      padding-bottom: 0.1875em;
      padding-inline-start: 0.375em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-code);
      font-weight: 600;
      font-size: 0.875em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: "`";
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: "`";
    }
    :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.875em;
    }
    :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.9em;
    }
    :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-pre-code);
      background-color: var(--tw-prose-pre-bg);
      overflow-x: auto;
      font-weight: 400;
      font-size: 0.875em;
      line-height: 1.7142857;
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
      border-radius: 0.375rem;
      padding-top: 0.8571429em;
      padding-inline-end: 1.1428571em;
      padding-bottom: 0.8571429em;
      padding-inline-start: 1.1428571em;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      background-color: transparent;
      border-width: 0;
      border-radius: 0;
      padding: 0;
      font-weight: inherit;
      color: inherit;
      font-size: inherit;
      font-family: inherit;
      line-height: inherit;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: none;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: none;
    }
    :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      width: 100%;
      table-layout: auto;
      margin-top: 2em;
      margin-bottom: 2em;
      font-size: 0.875em;
      line-height: 1.7142857;
    }
    :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-th-borders);
    }
    :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      vertical-align: bottom;
      padding-inline-end: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-inline-start: 0.5714286em;
    }
    :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-td-borders);
    }
    :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 0;
    }
    :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      vertical-align: baseline;
    }
    :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-top-width: 1px;
      border-top-color: var(--tw-prose-th-borders);
    }
    :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      vertical-align: top;
    }
    :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      text-align: start;
    }
    :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-captions);
      font-size: 0.875em;
      line-height: 1.4285714;
      margin-top: 0.8571429em;
    }
    --tw-prose-body: oklch(37.3% 0.034 259.733);
    --tw-prose-headings: oklch(21% 0.034 264.665);
    --tw-prose-lead: oklch(44.6% 0.03 256.802);
    --tw-prose-links: oklch(21% 0.034 264.665);
    --tw-prose-bold: oklch(21% 0.034 264.665);
    --tw-prose-counters: oklch(55.1% 0.027 264.364);
    --tw-prose-bullets: oklch(87.2% 0.01 258.338);
    --tw-prose-hr: oklch(92.8% 0.006 264.531);
    --tw-prose-quotes: oklch(21% 0.034 264.665);
    --tw-prose-quote-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-captions: oklch(55.1% 0.027 264.364);
    --tw-prose-kbd: oklch(21% 0.034 264.665);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(21% 0.034 264.665);
    --tw-prose-pre-code: oklch(92.8% 0.006 264.531);
    --tw-prose-pre-bg: oklch(27.8% 0.033 256.848);
    --tw-prose-th-borders: oklch(87.2% 0.01 258.338);
    --tw-prose-td-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-invert-body: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-bullets: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-hr: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-quotes: oklch(96.7% 0.003 264.542);
    --tw-prose-invert-quote-borders: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-captions: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
    --tw-prose-invert-th-borders: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-td-borders: oklch(37.3% 0.034 259.733);
    font-size: 1rem;
    line-height: 1.75;
    :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      padding-inline-start: 1.625em;
    }
    :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-top: 0.5714286em;
      padding-inline-end: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-inline-start: 0.5714286em;
    }
    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 0;
    }
  }
  .prose-sm {
    font-size: 0.875rem;
    line-height: 1.7142857;
    :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
      margin-bottom: 1.1428571em;
    }
    :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 1.2857143em;
      line-height: 1.5555556;
      margin-top: 0.8888889em;
      margin-bottom: 0.8888889em;
    }
    :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.3333333em;
      margin-bottom: 1.3333333em;
      padding-inline-start: 1.1111111em;
    }
    :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 2.1428571em;
      margin-top: 0;
      margin-bottom: 0.8em;
      line-height: 1.2;
    }
    :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 1.4285714em;
      margin-top: 1.6em;
      margin-bottom: 0.8em;
      line-height: 1.4;
    }
    :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 1.2857143em;
      margin-top: 1.5555556em;
      margin-bottom: 0.4444444em;
      line-height: 1.5555556;
    }
    :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.4285714em;
      margin-bottom: 0.5714286em;
      line-height: 1.4285714;
    }
    :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
    }
    :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
    }
    :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
    }
    :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
      border-radius: 0.3125rem;
      padding-top: 0.1428571em;
      padding-inline-end: 0.3571429em;
      padding-bottom: 0.1428571em;
      padding-inline-start: 0.3571429em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
    }
    :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.9em;
    }
    :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8888889em;
    }
    :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
      line-height: 1.6666667;
      margin-top: 1.6666667em;
      margin-bottom: 1.6666667em;
      border-radius: 0.25rem;
      padding-top: 0.6666667em;
      padding-inline-end: 1em;
      padding-bottom: 0.6666667em;
      padding-inline-start: 1em;
    }
    :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
      margin-bottom: 1.1428571em;
      padding-inline-start: 1.5714286em;
    }
    :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
      margin-bottom: 1.1428571em;
      padding-inline-start: 1.5714286em;
    }
    :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.2857143em;
      margin-bottom: 0.2857143em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.4285714em;
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.4285714em;
    }
    :where(.prose-sm > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5714286em;
      margin-bottom: 0.5714286em;
    }
    :where(.prose-sm > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
    }
    :where(.prose-sm > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.1428571em;
    }
    :where(.prose-sm > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
    }
    :where(.prose-sm > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.1428571em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5714286em;
      margin-bottom: 0.5714286em;
    }
    :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
      margin-bottom: 1.1428571em;
    }
    :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.1428571em;
    }
    :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.2857143em;
      padding-inline-start: 1.5714286em;
    }
    :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2.8571429em;
      margin-bottom: 2.8571429em;
    }
    :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
      line-height: 1.5;
    }
    :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 1em;
      padding-bottom: 0.6666667em;
      padding-inline-start: 1em;
    }
    :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-top: 0.6666667em;
      padding-inline-end: 1em;
      padding-bottom: 0.6666667em;
      padding-inline-start: 1em;
    }
    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
    }
    :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-size: 0.8571429em;
      line-height: 1.3333333;
      margin-top: 0.6666667em;
    }
    :where(.prose-sm > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(.prose-sm > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 0;
    }
  }
  .-mt-16 {
    margin-top: calc(var(--spacing) * -16);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .-mr-1 {
    margin-right: calc(var(--spacing) * -1);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }
  .mr-auto {
    margin-right: auto;
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .ml-0 {
    margin-left: calc(var(--spacing) * 0);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-auto {
    margin-left: auto;
  }
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .line-clamp-5 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
  }
  .line-clamp-8 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 8;
  }
  .\!hidden {
    display: none !important;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .list-item {
    display: list-item;
  }
  .table {
    display: table;
  }
  .table-caption {
    display: table-caption;
  }
  .table-cell {
    display: table-cell;
  }
  .table-row {
    display: table-row;
  }
  .aspect-\[4\/3\] {
    aspect-ratio: 4/3;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-25 {
    height: calc(var(--spacing) * 25);
  }
  .h-50 {
    height: calc(var(--spacing) * 50);
  }
  .h-75 {
    height: calc(var(--spacing) * 75);
  }
  .h-100 {
    height: calc(var(--spacing) * 100);
  }
  .h-\[42px\] {
    height: 42px;
  }
  .h-full {
    height: 100%;
  }
  .max-h-0 {
    max-height: calc(var(--spacing) * 0);
  }
  .max-h-8 {
    max-height: calc(var(--spacing) * 8);
  }
  .max-h-12 {
    max-height: calc(var(--spacing) * 12);
  }
  .max-h-20 {
    max-height: calc(var(--spacing) * 20);
  }
  .max-h-48 {
    max-height: calc(var(--spacing) * 48);
  }
  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }
  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }
  .max-h-\[80vh\] {
    max-height: 80vh;
  }
  .max-h-full {
    max-height: 100%;
  }
  .max-h-screen {
    max-height: 100vh;
  }
  .min-h-\[80px\] {
    min-height: 80px;
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-1\/4 {
    width: calc(1/4 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-3\/4 {
    width: calc(3/4 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-25 {
    width: calc(var(--spacing) * 25);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-50 {
    width: calc(var(--spacing) * 50);
  }
  .w-75 {
    width: calc(var(--spacing) * 75);
  }
  .w-100 {
    width: calc(var(--spacing) * 100);
  }
  .w-\[20px\] {
    width: 20px;
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-none {
    max-width: none;
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .shrink {
    flex-shrink: 1;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .origin-top-right {
    transform-origin: top right;
  }
  .-translate-y-2 {
    --tw-translate-y: calc(var(--spacing) * -2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-4 {
    --tw-translate-y: calc(var(--spacing) * -4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-95 {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .-rotate-90 {
    rotate: calc(90deg * -1);
  }
  .-rotate-180 {
    rotate: calc(180deg * -1);
  }
  .-rotate-270 {
    rotate: calc(270deg * -1);
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-ping {
    animation: var(--animate-ping);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .flex-wrap-reverse {
    flex-wrap: wrap-reverse;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }
  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }
  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }
  .-space-x-px {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(-1px * var(--tw-space-x-reverse));
      margin-inline-end: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }
  .gap-y-6 {
    row-gap: calc(var(--spacing) * 6);
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-t-full {
    border-top-left-radius: calc(infinity * 1px);
    border-top-right-radius: calc(infinity * 1px);
  }
  .rounded-l-md {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  .rounded-r-md {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }
  .rounded-b-full {
    border-bottom-right-radius: calc(infinity * 1px);
    border-bottom-left-radius: calc(infinity * 1px);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-b-4 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 4px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-l-12 {
    border-left-style: var(--tw-border-style);
    border-left-width: 12px;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-blue-400 {
    border-color: var(--color-blue-400);
  }
  .border-blue-500 {
    border-color: var(--color-blue-500);
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-blue-800 {
    border-color: var(--color-blue-800);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-gray-400 {
    border-color: var(--color-gray-400);
  }
  .border-gray-700 {
    border-color: var(--color-gray-700);
  }
  .border-green-400 {
    border-color: var(--color-green-400);
  }
  .border-red-400 {
    border-color: var(--color-red-400);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-yellow-400 {
    border-color: var(--color-yellow-400);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-blue-900 {
    background-color: var(--color-blue-900);
  }
  .bg-blue-950 {
    background-color: var(--color-blue-950);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-gray-800\/0 {
    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 0%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-800) 0%, transparent);
    }
  }
  .bg-gray-800\/100 {
    background-color: var(--color-gray-800);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-800 {
    background-color: var(--color-red-800);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/70 {
    background-color: color-mix(in srgb, #fff 70%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }
  .bg-white\/80 {
    background-color: color-mix(in srgb, #fff 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .fill-current {
    fill: currentcolor;
  }
  .object-contain {
    object-fit: contain;
  }
  .object-cover {
    object-fit: cover;
  }
  .object-center {
    object-position: center;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }
  .pr-9 {
    padding-right: calc(var(--spacing) * 9);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }
  .pb-0\.5 {
    padding-bottom: calc(var(--spacing) * 0.5);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pl-0 {
    padding-left: calc(var(--spacing) * 0);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .text-center {
    text-align: center;
  }
  .text-end {
    text-align: end;
  }
  .text-justify {
    text-align: justify;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-baseline {
    vertical-align: baseline;
  }
  .align-bottom {
    vertical-align: bottom;
  }
  .align-middle {
    vertical-align: middle;
  }
  .align-text-bottom {
    vertical-align: text-bottom;
  }
  .align-text-top {
    vertical-align: text-top;
  }
  .align-top {
    vertical-align: top;
  }
  .font-montserrat {
    font-family: var(--font-montserrat);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }
  .text-nowrap {
    text-wrap: nowrap;
  }
  .text-wrap {
    text-wrap: wrap;
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .whitespace-normal {
    white-space: normal;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .text-blue-400 {
    color: var(--color-blue-400);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-blue-900 {
    color: var(--color-blue-900);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-400 {
    color: var(--color-green-400);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-transparent {
    color: transparent;
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .not-italic {
    font-style: normal;
  }
  .ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .overline {
    text-decoration-line: overline;
  }
  .underline {
    text-decoration-line: underline;
  }
  .placeholder-gray-300 {
    &::placeholder {
      color: var(--color-gray-300);
    }
  }
  .placeholder-gray-400 {
    &::placeholder {
      color: var(--color-gray-400);
    }
  }
  .placeholder-gray-500 {
    &::placeholder {
      color: var(--color-gray-500);
    }
  }
  .accent-gray-500 {
    accent-color: var(--color-gray-500);
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-25 {
    opacity: 25%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-black {
    --tw-ring-color: var(--color-black);
  }
  .ring-blue-200 {
    --tw-ring-color: var(--color-blue-200);
  }
  .ring-blue-500 {
    --tw-ring-color: var(--color-blue-500);
  }
  .ring-offset-2 {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .\!filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,) !important;
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition\! {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events !important;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function)) !important;
    transition-duration: var(--tw-duration, var(--default-transition-duration)) !important;
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-75 {
    --tw-duration: 75ms;
    transition-duration: 75ms;
  }
  .duration-100 {
    --tw-duration: 100ms;
    transition-duration: 100ms;
  }
  .duration-150 {
    --tw-duration: 150ms;
    transition-duration: 150ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .ease-in {
    --tw-ease: var(--ease-in);
    transition-timing-function: var(--ease-in);
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .select-all {
    -webkit-user-select: all;
    user-select: all;
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .group-hover\:scale-110 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 110%;
        --tw-scale-y: 110%;
        --tw-scale-z: 110%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:text-blue-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-blue-700);
      }
    }
  }
  .required\:visible {
    &:required {
      visibility: visible;
    }
  }
  .hover\:border-blue-400 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-400);
      }
    }
  }
  .hover\:border-blue-500 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-500);
      }
    }
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:bg-blue-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-50);
      }
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-300 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-300);
      }
    }
  }
  .hover\:bg-gray-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700);
      }
    }
  }
  .hover\:bg-red-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-200);
      }
    }
  }
  .hover\:bg-red-900 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-900);
      }
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\:text-blue-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-700);
      }
    }
  }
  .hover\:text-blue-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .hover\:text-gray-200 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-200);
      }
    }
  }
  .hover\:text-gray-300 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-300);
      }
    }
  }
  .hover\:text-gray-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-500);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:ring-2 {
    &:hover {
      @media (hover: hover) {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:ring-blue-200 {
    &:hover {
      @media (hover: hover) {
        --tw-ring-color: var(--color-blue-200);
      }
    }
  }
  .focus\:border-blue-500 {
    &:focus {
      border-color: var(--color-blue-500);
    }
  }
  .focus\:border-gray-400 {
    &:focus {
      border-color: var(--color-gray-400);
    }
  }
  .focus\:border-transparent {
    &:focus {
      border-color: transparent;
    }
  }
  .focus\:underline {
    &:focus {
      text-decoration-line: underline;
    }
  }
  .focus\:ring-0 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:ring-gray-300 {
    &:focus {
      --tw-ring-color: var(--color-gray-300);
    }
  }
  .focus\:ring-gray-500 {
    &:focus {
      --tw-ring-color: var(--color-gray-500);
    }
  }
  .focus\:ring-gray-600 {
    &:focus {
      --tw-ring-color: var(--color-gray-600);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .active\:bg-gray-200 {
    &:active {
      background-color: var(--color-gray-200);
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .sm\:col-span-2 {
    @media (width >= 40rem) {
      grid-column: span 2 / span 2;
    }
  }
  .sm\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:ml-2 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 2);
    }
  }
  .sm\:grid {
    @media (width >= 40rem) {
      display: grid;
    }
  }
  .sm\:max-h-10 {
    @media (width >= 40rem) {
      max-height: calc(var(--spacing) * 10);
    }
  }
  .sm\:max-h-20 {
    @media (width >= 40rem) {
      max-height: calc(var(--spacing) * 20);
    }
  }
  .sm\:w-1\/4 {
    @media (width >= 40rem) {
      width: calc(1/4 * 100%);
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\:grid-cols-3 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:gap-4 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .sm\:p-6 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .sm\:text-sm {
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .sm\:text-xl {
    @media (width >= 40rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .md\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:ml-2 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 2);
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:aspect-auto {
    @media (width >= 48rem) {
      aspect-ratio: auto;
    }
  }
  .md\:h-12 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 12);
    }
  }
  .md\:max-h-12 {
    @media (width >= 48rem) {
      max-height: calc(var(--spacing) * 12);
    }
  }
  .md\:min-h-\[150px\] {
    @media (width >= 48rem) {
      min-height: 150px;
    }
  }
  .md\:w-1\/2 {
    @media (width >= 48rem) {
      width: calc(1/2 * 100%);
    }
  }
  .md\:w-1\/4 {
    @media (width >= 48rem) {
      width: calc(1/4 * 100%);
    }
  }
  .md\:w-1\/6 {
    @media (width >= 48rem) {
      width: calc(1/6 * 100%);
    }
  }
  .md\:w-3\/4 {
    @media (width >= 48rem) {
      width: calc(3/4 * 100%);
    }
  }
  .md\:w-12 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 12);
    }
  }
  .md\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-\[1fr_1fr_1fr_auto\] {
    @media (width >= 48rem) {
      grid-template-columns: 1fr 1fr 1fr auto;
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:justify-end {
    @media (width >= 48rem) {
      justify-content: flex-end;
    }
  }
  .md\:space-x-3 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:text-2xl {
    @media (width >= 48rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .md\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .lg\:mr-4 {
    @media (width >= 64rem) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:w-1\/2 {
    @media (width >= 64rem) {
      width: calc(1/2 * 100%);
    }
  }
  .lg\:w-1\/4 {
    @media (width >= 64rem) {
      width: calc(1/4 * 100%);
    }
  }
  .lg\:w-1\/5 {
    @media (width >= 64rem) {
      width: calc(1/5 * 100%);
    }
  }
  .lg\:w-1\/8 {
    @media (width >= 64rem) {
      width: calc(1/8 * 100%);
    }
  }
  .lg\:w-3\/4 {
    @media (width >= 64rem) {
      width: calc(3/4 * 100%);
    }
  }
  .lg\:w-4\/5 {
    @media (width >= 64rem) {
      width: calc(4/5 * 100%);
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-\[1fr_1fr_1fr_1fr_auto\] {
    @media (width >= 64rem) {
      grid-template-columns: 1fr 1fr 1fr 1fr auto;
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:gap-6 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:text-3xl {
    @media (width >= 64rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .lg\:text-sm {
    @media (width >= 64rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .xl\:w-1\/8 {
    @media (width >= 80rem) {
      width: calc(1/8 * 100%);
    }
  }
}
@layer base {
  html {
    font-family: 'Montserrat', 'Montserrat Fallback', 'Segoe UI', 'Roboto', 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
  }
}
@layer components {
  .btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background-color: var(--color-gray-700);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-white);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .btn-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background-color: var(--color-gray-200);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-gray-700);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-300);
      }
    }
  }
  .btn-danger {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background-color: var(--color-red-600);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-white);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  input[type="text"], input[type="email"], input[type="password"], input[type="number"], input[type="tel"], input[type="url"], input[type="date"], select, textarea {
    display: block;
    width: 100%;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-gray-300);
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 2);
    &::placeholder {
      color: var(--color-gray-400);
    }
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:focus {
      border-color: var(--color-blue-500);
    }
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .required::after {
    content: "*";
    margin-left: calc(var(--spacing) * 1);
    color: var(--color-red-500);
  }
  .border-l-12 {
    border-left-width: 12px;
  }
  input[type="checkbox"] {
    margin-right: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 4);
    width: calc(var(--spacing) * 4);
    flex-shrink: 0;
    border-radius: 0.25rem;
    border-color: var(--color-gray-500);
    color: var(--color-gray-500);
    accent-color: var(--color-gray-500);
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false;
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }
  }
}
